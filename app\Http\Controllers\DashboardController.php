<?php

namespace App\Http\Controllers;

use App\Models\Assessment;
use App\Models\AssessmentQuestion;
use App\Models\AssessmentResult;
use App\Models\ContentBlock;
use App\Models\SchoolClass;
use App\Models\Subject;
use App\Models\Topic;
use App\Models\Unit;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class DashboardController extends Controller
{
    public function index()
    {
        try {
            // Fetch classes and subjects
            $classes = SchoolClass::select('id', 'name')->get();
            $subjects = Subject::select('id', 'name', 'class_id')->get();

            // Fetch assessment statistics
            $assessmentStats = $this->getAssessmentStats();

            // Fetch student statistics
            $studentStats = $this->getStudentStats();

            // Fetch content statistics
            $contentStats = $this->getContentStats();

            // Fetch chart data
            $performanceBySubject = $this->getPerformanceBySubject();
            $assessmentCompletionData = $this->getAssessmentCompletionData();
            $contentTypeDistribution = $this->getContentTypeDistribution();

            // Fetch recent assessments
            $recentAssessments = $this->getRecentAssessments();

            return Inertia::render('dashboard', [
                'classes' => $classes,
                'subjects' => $subjects,
                'assessmentStats' => $assessmentStats,
                'studentStats' => $studentStats,
                'contentStats' => $contentStats,
                'performanceBySubject' => $performanceBySubject,
                'assessmentCompletionData' => $assessmentCompletionData,
                'contentTypeDistribution' => $contentTypeDistribution,
                'recentAssessments' => $recentAssessments,
            ]);
        } catch (QueryException $e) {
            Log::error('Dashboard query error: ' . $e->getMessage());

            // Provide default data structure for development
            return $this->renderDashboardWithDefaults();
        } catch (Exception $e) {
            Log::error('Dashboard error: ' . $e->getMessage());

            // Provide default data structure for development
            return $this->renderDashboardWithDefaults();
        }
    }

    /**
     * Render dashboard with default data (for development)
     */
    private function renderDashboardWithDefaults()
    {
        // Create default data for development
        $classes = [
            ['id' => 1, 'name' => 'Class 1'],
            ['id' => 2, 'name' => 'Class 2'],
            ['id' => 3, 'name' => 'Class 3'],
        ];

        $subjects = [
            ['id' => 1, 'name' => 'Mathematics', 'class_id' => 1],
            ['id' => 2, 'name' => 'Science', 'class_id' => 1],
            ['id' => 3, 'name' => 'English', 'class_id' => 2],
            ['id' => 4, 'name' => 'History', 'class_id' => 2],
            ['id' => 5, 'name' => 'Geography', 'class_id' => 3],
        ];

        $assessmentStats = [
            'total' => 24,
            'activeToday' => 5,
            'avgScore' => 72,
            'completionRate' => 68,
        ];

        $studentStats = [
            'total' => 150,
            'activeToday' => 87,
            'averagePerformance' => 76,
        ];

        $contentStats = [
            'totalTopics' => 48,
            'totalUnits' => 12,
            'totalContentBlocks' => 346,
        ];

        $performanceBySubject = [
            ['subject' => 'Mathematics', 'avgScore' => 68],
            ['subject' => 'Science', 'avgScore' => 75],
            ['subject' => 'English', 'avgScore' => 82],
            ['subject' => 'History', 'avgScore' => 77],
            ['subject' => 'Geography', 'avgScore' => 71],
        ];

        $assessmentCompletionData = [
            ['date' => 'Mon', 'completed' => 12, 'started' => 18],
            ['date' => 'Tue', 'completed' => 15, 'started' => 20],
            ['date' => 'Wed', 'completed' => 18, 'started' => 22],
            ['date' => 'Thu', 'completed' => 14, 'started' => 19],
            ['date' => 'Fri', 'completed' => 16, 'started' => 20],
            ['date' => 'Sat', 'completed' => 8, 'started' => 10],
            ['date' => 'Sun', 'completed' => 5, 'started' => 8],
        ];

        $contentTypeDistribution = [
            ['type' => 'Text', 'count' => 145],
            ['type' => 'Image', 'count' => 78],
            ['type' => 'Video', 'count' => 42],
            ['type' => 'Audio', 'count' => 28],
            ['type' => 'List', 'count' => 53],
        ];

        $recentAssessments = [
            ['id' => 1, 'title' => 'Algebra Test', 'subject' => 'Mathematics', 'class' => 'Class 1', 'submissions' => 28, 'avgScore' => 72, 'date' => '2023-08-01'],
            ['id' => 2, 'title' => 'Scientific Method', 'subject' => 'Science', 'class' => 'Class 1', 'submissions' => 25, 'avgScore' => 78, 'date' => '2023-08-03'],
            ['id' => 3, 'title' => 'Grammar Quiz', 'subject' => 'English', 'class' => 'Class 2', 'submissions' => 32, 'avgScore' => 85, 'date' => '2023-08-05'],
            ['id' => 4, 'title' => 'World War II', 'subject' => 'History', 'class' => 'Class 2', 'submissions' => 27, 'avgScore' => 76, 'date' => '2023-08-07'],
        ];

        return Inertia::render('dashboard', [
            'classes' => $classes,
            'subjects' => $subjects,
            'assessmentStats' => $assessmentStats,
            'studentStats' => $studentStats,
            'contentStats' => $contentStats,
            'performanceBySubject' => $performanceBySubject,
            'assessmentCompletionData' => $assessmentCompletionData,
            'contentTypeDistribution' => $contentTypeDistribution,
            'recentAssessments' => $recentAssessments,
        ]);
    }

    /**
     * Get assessment statistics
     */
    private function getAssessmentStats()
    {
        try {
            $total = Assessment::count();
            $activeToday = Assessment::whereDate('created_at', Carbon::today())->count();

            // Calculate average score from assessment results
            $avgScore = AssessmentResult::avg('score') ?? 0;

            // Calculate completion rate (completed assessments / total started)
            $startedCount = AssessmentResult::count();
            $completedCount = AssessmentResult::where('status', 'completed')->count();
            $completionRate = $startedCount > 0 ? ($completedCount / $startedCount) * 100 : 0;

            return [
                'total' => $total,
                'activeToday' => $activeToday,
                'avgScore' => round($avgScore, 0),
                'completionRate' => round($completionRate, 0),
            ];
        } catch (Exception $e) {
            Log::error('Error fetching assessment stats: ' . $e->getMessage());
            return [
                'total' => 0,
                'activeToday' => 0,
                'avgScore' => 0,
                'completionRate' => 0,
            ];
        }
    }

    /**
     * Get student statistics
     */
    private function getStudentStats()
    {
        try {
            $total = User::where('role', 'student')->count();
            $activeToday = User::where('role', 'student')
                ->whereDate('last_login_at', Carbon::today())
                ->count();

            // Calculate average performance from assessment results
            $avgPerformance = AssessmentResult::whereHas('user', function ($query) {
                $query->where('role', 'student');
            })->avg('score') ?? 0;

            return [
                'total' => $total,
                'activeToday' => $activeToday,
                'averagePerformance' => round($avgPerformance, 0),
            ];
        } catch (Exception $e) {
            Log::error('Error fetching student stats: ' . $e->getMessage());
            return [
                'total' => 0,
                'activeToday' => 0,
                'averagePerformance' => 0,
            ];
        }
    }

    /**
     * Get content statistics
     */
    private function getContentStats()
    {
        try {
            $totalTopics = Topic::count();
            $totalUnits = Unit::count();
            $totalContentBlocks = ContentBlock::count();

            return [
                'totalTopics' => $totalTopics,
                'totalUnits' => $totalUnits,
                'totalContentBlocks' => $totalContentBlocks,
            ];
        } catch (Exception $e) {
            Log::error('Error fetching content stats: ' . $e->getMessage());
            return [
                'totalTopics' => 0,
                'totalUnits' => 0,
                'totalContentBlocks' => 0,
            ];
        }
    }

    /**
     * Get performance by subject data for chart
     */
    private function getPerformanceBySubject()
    {
        try {
            $subjects = Subject::with(['topics.assessments.results'])
                ->get()
                ->map(function ($subject) {
                    $scores = collect();

                    foreach ($subject->topics as $topic) {
                        foreach ($topic->assessments as $assessment) {
                            $scores = $scores->merge($assessment->results->pluck('score'));
                        }
                    }

                    $avgScore = $scores->count() > 0 ? $scores->avg() : 0;

                    return [
                        'subject' => $subject->name,
                        'avgScore' => round($avgScore, 0),
                    ];
                });

            return $subjects;
        } catch (Exception $e) {
            Log::error('Error fetching performance by subject: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get assessment completion data for the last 7 days
     */
    private function getAssessmentCompletionData()
    {
        try {
            $data = [];
            $days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

            // Get last 7 days data
            for ($i = 6; $i >= 0; $i--) {
                $date = Carbon::now()->subDays($i);
                $dayName = $days[$date->dayOfWeek - 1]; // 1-based in Carbon, 0-based in our array

                $started = AssessmentResult::whereDate('created_at', $date)->count();
                $completed = AssessmentResult::whereDate('created_at', $date)
                    ->where('status', 'completed')
                    ->count();

                $data[] = [
                    'date' => $dayName,
                    'started' => $started,
                    'completed' => $completed,
                ];
            }

            return $data;
        } catch (Exception $e) {
            Log::error('Error fetching assessment completion data: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get content type distribution for chart
     */
    private function getContentTypeDistribution()
    {
        try {
            $types = ['text', 'image', 'video', 'audio', 'list'];
            $data = [];

            foreach ($types as $type) {
                $count = ContentBlock::where('type', $type)->count();

                if ($count > 0) {
                    $data[] = [
                        'type' => ucfirst($type),
                        'count' => $count,
                    ];
                }
            }

            return $data;
        } catch (Exception $e) {
            Log::error('Error fetching content type distribution: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get recent assessments with stats
     */
    private function getRecentAssessments()
    {
        try {
            return Assessment::with(['topic.subject.schoolClass', 'results'])
                ->latest()
                ->take(5)
                ->get()
                ->map(function ($assessment) {
                    $submissions = $assessment->results->count();
                    $avgScore = $submissions > 0 ? $assessment->results->avg('score') : 0;

                    return [
                        'id' => $assessment->id,
                        'title' => $assessment->name,
                        'subject' => $assessment->topic->subject->name,
                        'class' => $assessment->topic->subject->schoolClass->name,
                        'submissions' => $submissions,
                        'avgScore' => round($avgScore, 0),
                        'date' => $assessment->created_at->format('Y-m-d'),
                    ];
                });
        } catch (Exception $e) {
            Log::error('Error fetching recent assessments: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Filter dashboard data by class, subject, and date range
     */
    public function filter(Request $request)
    {
        try {
            $classId = $request->input('class_id', 'all');
            $subjectId = $request->input('subject_id', 'all');
            $dateFrom = $request->input('date_from', Carbon::now()->subDays(30)->format('Y-m-d'));
            $dateTo = $request->input('date_to', Carbon::now()->format('Y-m-d'));

            // Example of filtering assessments by class
            $query = Assessment::with(['topic.subject.schoolClass', 'results']);

            if ($classId !== 'all') {
                $query->whereHas('topic.subject.schoolClass', function ($q) use ($classId) {
                    $q->where('id', $classId);
                });
            }

            if ($subjectId !== 'all') {
                $query->whereHas('topic.subject', function ($q) use ($subjectId) {
                    $q->where('id', $subjectId);
                });
            }

            $filteredAssessments = $query->whereBetween('created_at', [$dateFrom, $dateTo])
                ->latest()
                ->take(5)
                ->get()
                ->map(function ($assessment) {
                    $submissions = $assessment->results->count();
                    $avgScore = $submissions > 0 ? $assessment->results->avg('score') : 0;

                    return [
                        'id' => $assessment->id,
                        'title' => $assessment->name,
                        'subject' => $assessment->topic->subject->name,
                        'class' => $assessment->topic->subject->schoolClass->name,
                        'submissions' => $submissions,
                        'avgScore' => round($avgScore, 0),
                        'date' => $assessment->created_at->format('Y-m-d'),
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => [
                    'recentAssessments' => $filteredAssessments,
                ]
            ]);
        } catch (Exception $e) {
            Log::error('Error filtering dashboard data: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while filtering dashboard data'
            ], 500);
        }
    }
}
