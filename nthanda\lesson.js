function getLessons(topicId) {
    var lessonData = axios
        .get(`http://*************:8000/api/lesson?topic_id=${topicId}`)
        .then(function (response) {
            return response.data;
        })
        .catch(function (error) {
            console.log(error);
            return [];
        });
    return lessonData;
}

function renderContentBlocks(lessonData) {
    const container = document.getElementById('content-blocks-container');
    container.innerHTML = '';

    if (lessonData.length > 0) {
        lessonData.forEach((block) => {
            const blockElement = renderContentBlock(block);
            if (blockElement) {
                container.appendChild(blockElement);
            }
        });

        const CompletedButton = document.createElement('button');
        CompletedButton.innerHTML = 'I Did It! 🎉';
        CompletedButton.className = 'complete-button';
        container.appendChild(CompletedButton);
    } else {
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'py-12 text-center';
        emptyMessage.innerHTML = '<p class="text-gray-500">No content has been added to this lesson yet.</p>';
        container.appendChild(emptyMessage);
    }
}

function renderContentBlock(block) {
    const blockElement = document.createElement('div');
    blockElement.className = 'content-block';
    blockElement.style.marginBottom = '0';

    switch (block.type) {
        case 'heading':
            blockElement.innerHTML = renderHeading(block);
            break;
        case 'paragraph':
            blockElement.innerHTML = renderParagraph(block);
            break;
        case 'image':
            blockElement.innerHTML = renderImage(block);
            break;
        case 'video':
            blockElement.innerHTML = renderVideo(block);
            break;
        case 'audio':
            blockElement.innerHTML = renderAudio(block);
            break;
        case 'list':
            blockElement.innerHTML = renderList(block);
            break;
        case 'zip':
            blockElement.innerHTML = renderHtml(block);
            break;
        default:
            return null;
    }

    return blockElement;
}

function renderHeading(block) {
    const level = block.attributes?.level || 'h2';
    let headingElement = '';

    switch (level) {
        case 'h1':
            headingElement = `<h1 class="heading-h1">${block.content}</h1>`;
            break;
        case 'h2':
            headingElement = `<h2 class="heading-h2">${block.content}</h2>`;
            break;
        case 'h3':
            headingElement = `<h3 class="heading-h3">${block.content}</h3>`;
            break;
        case 'h4':
            headingElement = `<h4 class="heading-h4">${block.content}</h4>`;
            break;
        case 'h5':
            headingElement = `<h5 class="heading-h5">${block.content}</h5>`;
            break;
        case 'h6':
            headingElement = `<h6 class="heading-h6">${block.content}</h6>`;
            break;
        default:
            headingElement = `<h2 class="heading-h2">${block.content}</h2>`;
    }

    return `
      <div>
          ${headingElement}
          ${renderNarrationButton(block)}
      </div>
  `;
}

// Render paragraph block
function renderParagraph(block) {
    return `
      <div class="content-block-container-row" style="margin-bottom: 8px;">
          <p class="paragraph"></p>
          ${block.content}${renderNarrationButton(block)} 
      </div>
  `;
}

// Render image block
function renderImage(block) {
    return `
      <div class="content-block-container-column" style="margin-bottom: 8px;">
          ${
              block.media_path
                  ? `
              <figure class="image-container">
                  <img
                      src="http://*************:8000/storage/${block.media_path}"
                      alt="${block.content || ''}"
                      class="image"
                  />
                  ${
                      block.content
                          ? `
                      <figcaption class="image-caption">${block.content}</figcaption>
                  `
                          : ''
                  }
              </figure>
          `
                  : ''
          }
          ${renderNarrationButton(block)}
      </div>
  `;
}

// Render video block
function renderVideo(block) {
    const videoUrl = block.attributes?.url;
    const videoPath = block.attributes?.video_path;

    return `
      <div class="content-block-container-column" style="margin-bottom: 8px;">
          <h3 class="video-title">${block.content}</h3>
          ${
              videoPath
                  ? `
              <div class="video-container">
                  <video controls class="video" src="http://*************:8000/storage/${videoPath}">
                      Your browser does not support the video tag.
                  </video>
              </div>
          `
                  : ''
          }
          ${
              !videoPath && videoUrl
                  ? `
              <div class="iframe-container">
                  <iframe
                      src="${videoUrl.includes('youtube.com') ? videoUrl.replace('watch?v=', 'embed/') : videoUrl}"
                      allowFullScreen
                      title="${block.content || 'Video'}"
                  ></iframe>
              </div>
          `
                  : ''
          }
      </div>
  `;
}

// Render audio block
function renderAudio(block) {
    return `
      <div class="content-block-container-column" style="margin-bottom: 8px;">
          <h3 class="audio-title">${block.content}</h3>
          ${
              block.audio_path
                  ? `
              <audio controls class="audio-player" src="http://*************:8000/storage/${block.audio_path}">
                  Your browser does not support the audio element.
              </audio>
          `
                  : ''
          }
      </div>
  `;
}

// Render list block
function renderList(block) {
    const items = block.attributes?.items || [];
    const listType = block.attributes?.listType || 'bullet';

    let listItems = '';
    items.forEach((item) => {
        listItems += `<li class="list-item">${item}</li>`;
    });

    return `
      <div class="content-block-container-row" style="margin-bottom: 8px;">
          ${
              listType === 'numbered'
                  ? `
              <ol class="numbered-list list-container">
                  ${listItems}
              </ol>
          `
                  : `
              <ul class="bullet-list list-container">
                  ${listItems}
              </ul>
          `
          }
          ${renderNarrationButton(block)}
      </div>
  `;
}

// Render narration button
function renderNarrationButton(block) {
    if (block.audio_path) {
        return `
          <div class="narration-container">
              <button onclick="playAudio('${block.audio_path}')" class="narration-button">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>
                      <path d="M15.54 8.46a5 5 0 0 1 0 7.07"></path>
                      <path d="M19.07 4.93a10 10 0 0 1 0 14.14"></path>
                  </svg>
                  
              </button>
              <audio id="audio-${block.id}" src="http://*************:8000/storage/${block.audio_path}" class="hidden"></audio>
          </div>
      `;
    }
    return '';
}

// Play audio narration with improved visual feedback
function playAudio(audioPath) {
    // Find all audio elements
    const audioElements = document.querySelectorAll(`audio[src="http://*************:8000/storage/${audioPath}"]`);

    // Get the button element
    const buttonElement = document.querySelector(`button[onclick="playAudio('${audioPath}')"]`);

    // Stop any currently playing audio
    document.querySelectorAll('audio').forEach((audio) => {
        const audioButton = document.querySelector(`button[onclick="playAudio('${audio.src.split('/storage/')[1]}')"]`);

        if (audio.src.includes(audioPath)) {
            // This is the one we want to play
        } else if (!audio.paused) {
            audio.pause();
            audio.currentTime = 0;

            // Reset other buttons
            if (audioButton) {
                audioButton.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>
              <path d="M15.54 8.46a5 5 0 0 1 0 7.07"></path>
              <path d="M19.07 4.93a10 10 0 0 1 0 14.14"></path>
          </svg>
          
        `;
                audioButton.style.backgroundColor = 'var(--color-blue)';
            }
        }
    });

    // Play the selected audio
    if (audioElements.length > 0) {
        const audioElement = audioElements[0];
        if (audioElement.paused) {
            audioElement.play().catch((error) => {
                console.error('Error playing audio:', error);
            });

            // Update button appearance
            if (buttonElement) {
                buttonElement.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="6" y="4" width="4" height="16"></rect>
              <rect x="14" y="4" width="4" height="16"></rect>
          </svg>
          Pause 🎵
        `;
                buttonElement.style.backgroundColor = 'var(--color-orange)';
            }

            // Add ended event to reset button
            audioElement.onended = function () {
                if (buttonElement) {
                    buttonElement.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>
                <path d="M15.54 8.46a5 5 0 0 1 0 7.07"></path>
                <path d="M19.07 4.93a10 10 0 0 1 0 14.14"></path>
            </svg>
            Listen again! 🔊
          `;
                    buttonElement.style.backgroundColor = 'var(--color-blue)';
                }
            };
        } else {
            // If already playing, pause it
            audioElement.pause();
            audioElement.currentTime = 0;

            // Reset button appearance
            if (buttonElement) {
                buttonElement.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>
              <path d="M15.54 8.46a5 5 0 0 1 0 7.07"></path>
              <path d="M19.07 4.93a10 10 0 0 1 0 14.14"></path>
          </svg>
          Listen to me! 🔊
        `;
                buttonElement.style.backgroundColor = 'var(--color-blue)';
            }
        }
    } else {
        console.error('Audio element not found for path:', audioPath);
    }
}

function renderHtml(block) {
    return `
      <div class="content-block-container-column" style="margin-bottom: 8px;">
          <div class="html-content" style="white-space: pre-wrap; width: 100%;">
              <iframe src="http://*************:8000/storage/${block.attributes?.index_path}" title="HTML5 Content" sandbox="allow-scripts allow-same-origin" style="width: 100%; height: 500px;"></iframe>
          </div>
      </div>
  `;
}
