import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import axios from 'axios';
import { addDays, format } from 'date-fns';
import { Activity, BookOpen, FileText, TrendingDown, TrendingUp, Users } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Bar, <PERSON>hart, CartesianGrid, Cell, Legend, Line, LineChart, Pie, Pie<PERSON>hart, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';

interface DashboardProps {
    classes: Array<{
        id: number;
        name: string;
    }>;
    subjects: Array<{
        id: number;
        name: string;
        class_id: number;
    }>;
    assessmentStats: {
        total: number;
        activeToday: number;
        avgScore: number;
        completionRate: number;
    };
    studentStats: {
        total: number;
        activeToday: number;
        averagePerformance: number;
    };
    contentStats: {
        totalTopics: number;
        totalUnits: number;
        totalContentBlocks: number;
    };
    performanceBySubject: Array<{
        subject: string;
        avgScore: number;
    }>;
    assessmentCompletionData: Array<{
        date: string;
        completed: number;
        started: number;
    }>;
    contentTypeDistribution: Array<{
        type: string;
        count: number;
    }>;
    recentAssessments: Array<{
        id: number;
        title: string;
        subject: string;
        class: string;
        submissions: number;
        avgScore: number;
        date: string;
    }>;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

// Colors for pie chart
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

export default function Dashboard({
    classes,
    subjects,
    assessmentStats,
    studentStats,
    contentStats,
    performanceBySubject,
    assessmentCompletionData,
    contentTypeDistribution,
    recentAssessments: initialAssessments,
}: DashboardProps) {
    // Filter states
    const [selectedClass, setSelectedClass] = useState<string>('all');
    const [selectedSubject, setSelectedSubject] = useState<string>('all');
    const [dateRange] = useState({
        from: addDays(new Date(), -30),
        to: new Date(),
    });
    const [filteredSubjects, setFilteredSubjects] = useState(subjects);
    const [recentAssessments, setRecentAssessments] = useState(initialAssessments);
    const [isLoading, setIsLoading] = useState(false);

    // Update filtered subjects when class changes
    useEffect(() => {
        if (selectedClass === 'all') {
            setFilteredSubjects(subjects);
        } else {
            setFilteredSubjects(subjects.filter((subject) => subject.class_id === Number(selectedClass)));
        }
    }, [selectedClass, subjects]);

    // Filter data when filters change
    useEffect(() => {
        submitFilter();
    }, [selectedClass, selectedSubject]);

    // Handle form submission for filtering data
    const submitFilter = async () => {
        try {
            setIsLoading(true);
            const response = await axios.post('/dashboard/filter', {
                class_id: selectedClass,
                subject_id: selectedSubject,
                date_from: format(dateRange.from, 'yyyy-MM-dd'),
                date_to: format(dateRange.to, 'yyyy-MM-dd'),
            });

            if (response.data.success) {
                setRecentAssessments(response.data.data.recentAssessments);
            }
        } catch (error) {
            console.error('Error filtering dashboard data:', error);
        } finally {
            setIsLoading(false);
        }
    };

    // Format percentage for display
    const formatPercentage = (value: number): string => `${value}%`;

    // Determine if a metric is improving or declining
    const getMetricTrend = (current: number, threshold: number): boolean => {
        return current > threshold;
    };

    // Custom tooltip formatter for charts
    const customTooltipFormatter = (value: number, name: string): [string, string] => {
        if (name === 'avgScore') {
            return [`${value}%`, 'Average Score'];
        }
        if (name === 'completed') {
            return [`${value}`, 'Completed'];
        }
        if (name === 'started') {
            return [`${value}`, 'Started'];
        }
        return [`${value}`, name];
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />
            <div className="flex flex-col gap-6 p-6">
                {/* Filters */}
                <div className="bg-card flex flex-col gap-4 rounded-xl border p-4 shadow sm:flex-row sm:items-center">
                    <div className="flex-1 space-y-1">
                        <h2 className="text-lg font-semibold">Dashboard Overview</h2>
                        <p className="text-muted-foreground text-sm">View and analyze assessment and performance data</p>
                    </div>
                    <div className="flex flex-col gap-3 sm:flex-row">
                        <div className="w-full sm:w-auto">
                            <div className="flex items-center gap-2 rounded-md border px-3 py-2">
                                <span className="text-sm">Last 30 Days</span>
                            </div>
                    </div>
                        <Select
                            value={selectedClass}
                            onValueChange={(value) => {
                                setSelectedClass(value);
                                if (value !== 'all') {
                                    setSelectedSubject('all');
                                }
                            }}
                        >
                            <SelectTrigger className="w-full sm:w-[180px]">
                                <SelectValue placeholder="Select Class" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Classes</SelectItem>
                                {classes.map((classItem) => (
                                    <SelectItem key={classItem.id} value={String(classItem.id)}>
                                        {classItem.name}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        <Select
                            value={selectedSubject}
                            onValueChange={(value) => {
                                setSelectedSubject(value);
                                setTimeout(submitFilter, 10);
                            }}
                        >
                            <SelectTrigger className="w-full sm:w-[180px]">
                                <SelectValue placeholder="Select Subject" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Subjects</SelectItem>
                                {filteredSubjects.map((subject) => (
                                    <SelectItem key={subject.id} value={String(subject.id)}>
                                        {subject.name}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                </div>

                {/* Stats Overview */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between pb-2">
                            <CardTitle className="text-sm font-medium">Assessments</CardTitle>
                            <FileText className="text-muted-foreground h-4 w-4" />
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-baseline justify-between">
                                <div className="text-2xl font-bold">{assessmentStats.total}</div>
                                <div className="flex items-center gap-1 text-xs">
                                    <span className="font-medium">Today:</span>
                                    <span>{assessmentStats.activeToday}</span>
                                </div>
                            </div>
                            <div className="mt-4 grid grid-cols-2 gap-4">
                                <div className="flex flex-col">
                                    <span className="text-muted-foreground text-xs">Avg. Score</span>
                                    <div className="flex items-center gap-1">
                                        <span className="text-lg font-bold">{formatPercentage(assessmentStats.avgScore)}</span>
                                        {getMetricTrend(assessmentStats.avgScore, 70) ? (
                                            <TrendingUp className="h-4 w-4 text-green-500" />
                                        ) : (
                                            <TrendingDown className="h-4 w-4 text-red-500" />
                                        )}
                                    </div>
                                </div>
                                <div className="flex flex-col">
                                    <span className="text-muted-foreground text-xs">Completion Rate</span>
                                    <div className="flex items-center gap-1">
                                        <span className="text-lg font-bold">{formatPercentage(assessmentStats.completionRate)}</span>
                                        {getMetricTrend(assessmentStats.completionRate, 65) ? (
                                            <TrendingUp className="h-4 w-4 text-green-500" />
                                        ) : (
                                            <TrendingDown className="h-4 w-4 text-red-500" />
                                        )}
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between pb-2">
                            <CardTitle className="text-sm font-medium">Students</CardTitle>
                            <Users className="text-muted-foreground h-4 w-4" />
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-baseline justify-between">
                                <div className="text-2xl font-bold">{studentStats.total}</div>
                                <div className="flex items-center gap-1 text-xs">
                                    <span className="font-medium">Active Today:</span>
                                    <span>{studentStats.activeToday}</span>
                                </div>
                            </div>
                            <div className="mt-4 flex items-baseline justify-between">
                                <div className="flex flex-col">
                                    <span className="text-muted-foreground text-xs">Avg. Performance</span>
                                    <div className="flex items-center gap-1">
                                        <span className="text-lg font-bold">{formatPercentage(studentStats.averagePerformance)}</span>
                                        {getMetricTrend(studentStats.averagePerformance, 75) ? (
                                            <TrendingUp className="h-4 w-4 text-green-500" />
                                        ) : (
                                            <TrendingDown className="h-4 w-4 text-red-500" />
                                        )}
                                    </div>
                                </div>
                                <div className="flex flex-col items-end">
                                    <div className="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-full">
                                        <Activity className="text-primary h-4 w-4" />
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between pb-2">
                            <CardTitle className="text-sm font-medium">Content</CardTitle>
                            <BookOpen className="text-muted-foreground h-4 w-4" />
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-baseline justify-between">
                                <div className="text-2xl font-bold">{contentStats.totalContentBlocks}</div>
                                <div className="flex items-center gap-1 text-xs">
                                    <span className="font-medium">Content Blocks</span>
                                </div>
                            </div>
                            <div className="mt-4 grid grid-cols-2 gap-4">
                                <div className="flex flex-col">
                                    <span className="text-muted-foreground text-xs">Topics</span>
                                    <span className="text-lg font-bold">{contentStats.totalTopics}</span>
                                </div>
                                <div className="flex flex-col">
                                    <span className="text-muted-foreground text-xs">Units</span>
                                    <span className="text-lg font-bold">{contentStats.totalUnits}</span>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Charts Section */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    <Card className="col-span-full lg:col-span-2">
                        <CardHeader>
                            <CardTitle>Assessment Completion Trends</CardTitle>
                            <CardDescription>Weekly overview of started vs completed assessments</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ResponsiveContainer width="100%" height={270}>
                                <BarChart data={assessmentCompletionData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                                    <CartesianGrid strokeDasharray="3 3" />
                                    <XAxis dataKey="date" />
                                    <YAxis />
                                    <Tooltip formatter={customTooltipFormatter} />
                                    <Legend />
                                    <Bar dataKey="started" fill="#8884d8" name="Started" />
                                    <Bar dataKey="completed" fill="#82ca9d" name="Completed" />
                                </BarChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>

                    <Card className="lg:row-span-2">
                        <CardHeader>
                            <CardTitle>Content Type Distribution</CardTitle>
                            <CardDescription>Breakdown of content by type</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ResponsiveContainer width="100%" height={270}>
                                <PieChart>
                                    <Pie
                                        data={contentTypeDistribution}
                                        cx="50%"
                                        cy="50%"
                                        labelLine={false}
                                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                        outerRadius={80}
                                        fill="#8884d8"
                                        dataKey="count"
                                        nameKey="type"
                                    >
                                        {contentTypeDistribution.map((entry, index) => (
                                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                        ))}
                                    </Pie>
                                    <Tooltip formatter={(value: number) => [`${value} blocks`, 'Count']} />
                                    <Legend />
                                </PieChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>

                    <Card className="col-span-full lg:col-span-2">
                        <CardHeader>
                            <CardTitle>Subject Performance Analysis</CardTitle>
                            <CardDescription>Average scores across different subjects</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ResponsiveContainer width="100%" height={270}>
                                <LineChart data={performanceBySubject} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                                    <CartesianGrid strokeDasharray="3 3" />
                                    <XAxis dataKey="subject" />
                                    <YAxis domain={[0, 100]} />
                                    <Tooltip formatter={(value: number) => [`${value}%`, 'Average Score']} />
                                    <Legend />
                                    <Line type="monotone" dataKey="avgScore" stroke="#82ca9d" name="Average Score" activeDot={{ r: 8 }} />
                                </LineChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>
                </div>

                {/* Recent Assessments */}
                <Card>
                    <CardHeader>
                        <CardTitle>Recent Assessments</CardTitle>
                        <CardDescription>Overview of the latest assessment activities</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Tabs defaultValue="all">
                            <TabsList className="mb-4">
                                <TabsTrigger value="all">All</TabsTrigger>
                                <TabsTrigger value="high">High Performance</TabsTrigger>
                                <TabsTrigger value="low">Needs Attention</TabsTrigger>
                            </TabsList>
                            <TabsContent value="all" className="space-y-0">
                                <div className="rounded-md border">
                                    <div className="bg-muted/50 grid grid-cols-6 p-4 text-sm font-medium">
                                        <div>Assessment</div>
                                        <div>Subject</div>
                                        <div>Class</div>
                                        <div className="text-center">Submissions</div>
                                        <div className="text-center">Avg. Score</div>
                                        <div className="text-right">Date</div>
                                    </div>
                                    <div className="divide-y">
                                        {isLoading ? (
                                            <div className="p-8 text-center">Loading...</div>
                                        ) : recentAssessments.length > 0 ? (
                                            recentAssessments.map((assessment) => (
                                                <div key={assessment.id} className="grid grid-cols-6 items-center p-4">
                                                    <div className="font-medium">{assessment.title}</div>
                                                    <div className="text-sm">{assessment.subject}</div>
                                                    <div className="text-sm">{assessment.class}</div>
                                                    <div className="text-center">{assessment.submissions}</div>
                                                    <div className="flex items-center justify-center">
                                                        <span
                                                            className={`rounded-full px-2 py-1 text-xs font-medium ${
                                                                assessment.avgScore >= 80
                                                                    ? 'bg-green-100 text-green-800'
                                                                    : assessment.avgScore >= 70
                                                                      ? 'bg-yellow-100 text-yellow-800'
                                                                      : 'bg-red-100 text-red-800'
                                                            }`}
                                                        >
                                                            {formatPercentage(assessment.avgScore)}
                                                        </span>
                                                    </div>
                                                    <div className="text-muted-foreground text-right text-sm">
                                                        {format(new Date(assessment.date), 'MMM d, yyyy')}
                                                    </div>
                                                </div>
                                            ))
                                        ) : (
                                            <div className="p-8 text-center">No assessments found</div>
                                        )}
                                    </div>
                                </div>
                            </TabsContent>

                            <TabsContent value="high" className="space-y-0">
                                <div className="rounded-md border">
                                    <div className="bg-muted/50 grid grid-cols-6 p-4 text-sm font-medium">
                                        <div>Assessment</div>
                                        <div>Subject</div>
                                        <div>Class</div>
                                        <div className="text-center">Submissions</div>
                                        <div className="text-center">Avg. Score</div>
                                        <div className="text-right">Date</div>
                                    </div>
                                    <div className="divide-y">
                                        {recentAssessments
                                            .filter((a) => a.avgScore >= 80)
                                            .map((assessment) => (
                                                <div key={assessment.id} className="grid grid-cols-6 items-center p-4">
                                                    <div className="font-medium">{assessment.title}</div>
                                                    <div className="text-sm">{assessment.subject}</div>
                                                    <div className="text-sm">{assessment.class}</div>
                                                    <div className="text-center">{assessment.submissions}</div>
                                                    <div className="flex items-center justify-center">
                                                        <span className="rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
                                                            {formatPercentage(assessment.avgScore)}
                                                        </span>
                                                    </div>
                                                    <div className="text-muted-foreground text-right text-sm">
                                                        {format(new Date(assessment.date), 'MMM d, yyyy')}
                                                    </div>
                                                </div>
                                            ))}
                                    </div>
                                </div>
                            </TabsContent>

                            <TabsContent value="low" className="space-y-0">
                                <div className="rounded-md border">
                                    <div className="bg-muted/50 grid grid-cols-6 p-4 text-sm font-medium">
                                        <div>Assessment</div>
                                        <div>Subject</div>
                                        <div>Class</div>
                                        <div className="text-center">Submissions</div>
                                        <div className="text-center">Avg. Score</div>
                                        <div className="text-right">Date</div>
                                    </div>
                                    <div className="divide-y">
                                        {recentAssessments
                                            .filter((a) => a.avgScore < 70)
                                            .map((assessment) => (
                                                <div key={assessment.id} className="grid grid-cols-6 items-center p-4">
                                                    <div className="font-medium">{assessment.title}</div>
                                                    <div className="text-sm">{assessment.subject}</div>
                                                    <div className="text-sm">{assessment.class}</div>
                                                    <div className="text-center">{assessment.submissions}</div>
                                                    <div className="flex items-center justify-center">
                                                        <span className="rounded-full bg-red-100 px-2 py-1 text-xs font-medium text-red-800">
                                                            {formatPercentage(assessment.avgScore)}
                                                        </span>
                                                    </div>
                                                    <div className="text-muted-foreground text-right text-sm">
                                                        {format(new Date(assessment.date), 'MMM d, yyyy')}
                                                    </div>
                                                </div>
                                            ))}
                                    </div>
                                </div>
                            </TabsContent>
                        </Tabs>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
